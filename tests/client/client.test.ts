import request from 'supertest';

import app from '../../app';
import models from '../../models';
import { clientRepository, featureRepository } from '../../repositories';
import redisService from '../../services/redis';
import { ClientFeatureType, CreateClientBodyType } from '../../types';
import { authSetup, rateLimiterSetup, transactionSetup } from '../setup';
import clientMock from './__mocks__/client';
import clientWithNumberOfUsers from './__mocks__/clientsWithNumberOfUsers';

jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');
jest.mock('../../models');

beforeAll(async () => {
  rateLimiterSetup.basicRateLimiterSetup();
  authSetup.superadminAuthSetup();
});

afterAll(async () => {
  await redisService.quit();
});

describe('Client Controller', () => {
  test('It should get client', async () => {
    const mockGetClient = jest.spyOn(clientRepository, 'getClient');
    mockGetClient.mockImplementation(() => Promise.resolve(clientMock));

    const response = await request(app).get('/api/client/:id');

    expect(response.statusCode).toBe(200);
    expect(response.body).toHaveProperty('id');
    expect(response.body).toHaveProperty('name');
    expect(response.body).toHaveProperty('createdAt');
    expect(response.body).toHaveProperty('updatedAt');
  });

  test('It should return 404 when client is not found', async () => {
    const mockGetClient = jest.spyOn(clientRepository, 'getClient');
    mockGetClient.mockImplementation(() => Promise.resolve(null));

    const response = await request(app).get('/api/client/:id');

    expect(response.statusCode).toBe(404);
  });

  test('It should get all Clients', async () => {
    const mockGetClients = jest.spyOn(clientRepository, 'getClients');
    mockGetClients.mockImplementation(() => Promise.resolve(clientWithNumberOfUsers));

    const response = await request(app).get('/api/client');

    expect(response.statusCode).toBe(200);
    expect(response.body).toBeInstanceOf(Array);
    expect(response.body[0]).toHaveProperty('id');
    expect(response.body[0]).toHaveProperty('name');
    expect(response.body[0]).toHaveProperty('createdAt');
    expect(response.body[0]).toHaveProperty('updatedAt');
    expect(response.body[0]).toHaveProperty('numberOfUsers');
  });

  test('It should create a new Client', async () => {
    const body: CreateClientBodyType = {
      name: 'New Client',
      industry: 'Unknown',
      emailDomains: ['demo.com'],
    };

    transactionSetup.mockTransaction(models);

    const mockCreateClient = jest.spyOn(clientRepository, 'createClient');
    mockCreateClient.mockImplementation(() => Promise.resolve(clientMock));

    const mockCreateClientFeatures = jest.spyOn(featureRepository, 'createClientFeatures');
    mockCreateClientFeatures.mockImplementation(() => Promise.resolve({} as ClientFeatureType));

    const mockGetAllFeatures = jest.spyOn(featureRepository, 'getAllFeatures');
    mockGetAllFeatures.mockImplementation(() => Promise.resolve([]));

    const response = await request(app).post('/api/client').send(body);

        console.log('status: ' + response.statusCode);
    console.log(response);

    expect(response.statusCode).toBe(200);
    expect(response.body).toHaveProperty('id');
    expect(response.body).toHaveProperty('name');
    expect(response.body).toHaveProperty('createdAt');
    expect(response.body).toHaveProperty('updatedAt');
  });

  test('It should delete the Client', async () => {
    const NUMBER_OF_DELETED_ROWS = 1;
    const mockDeleteClient = jest.spyOn(clientRepository, 'deleteClient');
    mockDeleteClient.mockImplementation(() => Promise.resolve(NUMBER_OF_DELETED_ROWS));

    const response = await request(app).delete('/api/client/:id');

    expect(response.statusCode).toBe(200);
  });

  test('It should return 404 when Client to delete is not found', async () => {
    const NUMBER_OF_DELETED_ROWS = 0;
    const mockDeleteClient = jest.spyOn(clientRepository, 'deleteClient');
    mockDeleteClient.mockImplementation(() => Promise.resolve(NUMBER_OF_DELETED_ROWS));

    const response = await request(app).delete('/api/client/:id');

    expect(response.statusCode).toBe(404);
  });

  test('It should return 403 if user with user role tries any of /client endpoints', async () => {
    authSetup.userAuthSetup();

    const getClientResponse = await request(app).get('/api/client/:id');
    const getClientsResponse = await request(app).get('/api/client');
    const createClientResponse = await request(app).post('/api/client');
    const deleteClientResponse = await request(app).delete('/api/client/:id');

    expect(getClientResponse.statusCode).toBe(403);
    expect(getClientsResponse.statusCode).toBe(403);
    expect(createClientResponse.statusCode).toBe(403);
    expect(deleteClientResponse.statusCode).toBe(403);
  });

  test('It should return 403 if user with admin role tries any of /client endpoints', async () => {
    authSetup.adminAuthSetup();

    const getClientResponse = await request(app).get('/api/client/:id');
    const getClientsResponse = await request(app).get('/api/client');
    const createClientResponse = await request(app).post('/api/client');
    const deleteClientResponse = await request(app).delete('/api/client/:id');

    expect(getClientResponse.statusCode).toBe(403);
    expect(getClientsResponse.statusCode).toBe(403);
    expect(createClientResponse.statusCode).toBe(403);
    expect(deleteClientResponse.statusCode).toBe(403);
  });
});
