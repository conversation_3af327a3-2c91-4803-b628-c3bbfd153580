'use strict';
import { QueryInterface } from 'sequelize';
import { dateFormats, decimalPoints } from '../enums';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn(
        'Clients',
        'dateFormat',
        {
          type: Sequelize.ENUM(...Object.values(dateFormats)),
          allowNull: false,
          defaultValue: dateFormats['DD MONTH YYYY'],
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'Clients',
        'decimalPoint',
        {
          type: Sequelize.ENUM(...Object.values(decimalPoints)),
          allowNull: false,
          defaultValue: decimalPoints.DOT,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn(
        'Clients',
        'dateFormat',
        {
          type: Sequelize.ENUM(...Object.values(dateFormats)),
          allowNull: false,
          defaultValue: dateFormats['YYYY-MM-DD'],
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        'Clients',
        'decimalPoint',
        {
          type: Sequelize.ENUM(...Object.values(decimalPoints)),
          allowNull: false,
          defaultValue: decimalPoints.SPACE,
        },
        { transaction },
      );
    });
  },
};
