import { getCashPool, getCashPoolBatches, makeCashPoolCalculations } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { batchStatus, reportEnum } from '~/enums';
import { setCashPool, updateCashPoolTrigger } from '~/reducers/cashPool.slice';
import { updateField } from '~/reducers/payment.slice';
import { routesEnum } from '~/routes';
import { LoadingSpinner, Text } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { formatDateString } from '~/utils/dates';
import { errorHandler } from '~/utils/errors';
import { displayNumber2 } from '~/utils/strings';

function getCashPoolBatchData(batch, userInfo) {
  const { dateFormat, decimalPoint } = userInfo;
  const {
    id,
    cashPool,
    cashPoolId,
    startDate,
    endDate,
    createdAt,
    createdByUser,
    totalInterestPayableToLeader,
    totalInterestReceivableFromLeader,
    status,
    isPooling,
  } = batch;
  const numberDisplayOptions = { decimalPoint, defaultValue: '-' };

  return {
    batchId: id,
    cashPoolId,
    cashPoolType: cashPool.type,
    startDate: formatDateString(startDate, dateFormat),
    endDate: formatDateString(endDate, dateFormat),
    dateUploaded: formatDateString(createdAt, dateFormat),
    user: createdByUser.fullName || createdByUser.username,
    totalInterestPayableToLeader: displayNumber2(totalInterestPayableToLeader, numberDisplayOptions),
    totalInterestReceivableFromLeader: displayNumber2(totalInterestReceivableFromLeader, numberDisplayOptions),
    status,
    isPooling,
  };
}

export function getCashPoolBatchesData(data = [], userInfo) {
  return data.map((item) => getCashPoolBatchData(item, userInfo));
}

const statusMapper = (status) => {
  switch (status) {
    case batchStatus.UNPAID:
      return 'Unpublished';
    case batchStatus.PAID:
      return 'Published';
    case batchStatus.PARTIALLY_PAID:
      return 'Partially Published';
    default:
      return status;
  }
};

export const columns = [
  { label: 'Start Date', sortBy: 'startDate', value: 'startDate' },
  { label: 'End Date', sortBy: 'endDate', value: 'endDate' },
  { label: 'Date Uploaded', sortBy: 'dateUploaded', value: 'dateUploaded' },
  { label: 'User', sortBy: 'user', value: 'user' },
  {
    label: 'Leader Interest Receivable',
    sortBy: 'totalInterestReceivableFromLeader',
    value: 'totalInterestReceivableFromLeader',
  },
  {
    label: 'Leader Interest Payable',
    sortBy: 'totalInterestPayableToLeader',
    value: 'totalInterestPayableToLeader',
  },
  {
    label: 'Status',
    sortBy: 'status',
    value: 'status',
    renderCustomCell: ({ status }) => {
      const color = status === 'Unpooled' ? 'blaze-orange' : 'deep-sapphire';
      return (
        <Text color={color} variant="m-spaced">
          {statusMapper(status)}
        </Text>
      );
    },
  },
];

export const renderTableActionColumn = ({
  item,
  lastBatchId,
  batches,
  setBatches,
  setIsDeleteModalShowing,
  history,
  dispatch,
  setIsPooling,
}) => {
  const { cashPoolId, batchId, status, cashPoolType } = item;
  const options = [];

  const firstUnpooledBatch = batches
    .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
    .find((batch) => batch.status === 'Unpooled');

  if (status === batchStatus.UNPOOLED && firstUnpooledBatch.id === batchId) {
    options.push({
      label: 'Pool Cash',
      onClick: async () => {
        setIsPooling(batchId, true);
        try {
          await makeCashPoolCalculations({ cashPoolId, cashPoolType, batchId });
          const [cashPool] = await Promise.all([
            getCashPool({ cashPoolId }),
            getCashPoolBatches({ cashPoolId }).then(setBatches),
          ]);
          dispatch(setCashPool(cashPool));
          dispatch(updateCashPoolTrigger());
          showToast('Cash successfully pooled.');
        } catch (err) {
          errorHandler(err);
        } finally {
          setIsPooling(batchId, false);
        }
      },
    });
  }

  if (status !== batchStatus.UNPOOLED) {
    options.push({
      label: 'Show All Interest',
      onClick: () => {
        dispatch(updateField({ batchId }));
        history.push(`${routesEnum.PAYMENTS}?reportType=${reportEnum.CASH_POOL}`);
      },
    });
  }

  // Feature removed: June 4th 2025
  // options.push({
  //   label: 'Download file',
  //   onClick: async () => {
  //     try {
  //       const file = await getBatchFile({ cashPoolId, batchId });
  //       saveAs(file, file.name);
  //       showToast(`${file.name} successfully downloaded.`);
  //     } catch (err) {
  //       if (err?.response?.status === 404) return showErrorToast('No file found for this batch.');
  //       errorHandler(err);
  //     }
  //   },
  // });

  if ((status === batchStatus.UNPOOLED || status === batchStatus.UNPAID) && lastBatchId === batchId) {
    options.push({
      label: 'Delete',
      onClick: () => setIsDeleteModalShowing(item),
    });
  }

  return item.isPooling ? <LoadingSpinner size="s" /> : <ThreeDotActionMenu options={options} />;
};
