import { col, fn } from 'sequelize';
import models from '../models';
import { UpdateClientBody } from '../schemas/clientSchemas/updateClientSchema';
import { ClientType, ClientWithNumberOfUsersType, CreateClientType } from '../types';

const { Client, User } = models;

function getClient(clientId: number): Promise<ClientType | null> {
  return Client.findOne({ where: { id: clientId } });
}

function getClients(): Promise<ClientWithNumberOfUsersType[]> {
  return Client.findAll({
    order: [['name', 'ASC']],
    include: { model: User, as: 'users', attributes: [] },
    attributes: { include: [[fn('COUNT', col('users.id')), 'numberOfUsers']] },
    group: ['Client.id'],
  });
}

function createClient(client: CreateClientType): Promise<ClientType> {
  return Client.create(client);
}

async function updateClient(clientId: number, data: UpdateClientBody): Promise<ClientType> {
  const [, [client]] = await Client.update(data, { where: { id: clientId }, returning: true });

  return client;
}

function deleteClient(id: string): Promise<number> {
  return Client.destroy({ where: { id } });
}

export = { getClient, getClients, createClient, updateClient, deleteClient };
