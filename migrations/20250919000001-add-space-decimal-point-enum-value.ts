'use strict';
import { QueryInterface } from 'sequelize';
import { dateFormats, decimalPoints } from '../enums';

export = {
  up: async (queryInterface: QueryInterface, Sequelize: any) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // First, add 'space' value to the existing Users decimalPoint enum
      await queryInterface.sequelize.query(`ALTER TYPE "enum_Users_decimalPoint" ADD VALUE IF NOT EXISTS 'space';`, {
        transaction,
      });

      // Add the missing columns to the Clients table
      // Note: The decimalPoint enum will be created with all values including 'space'
      await queryInterface.addColumn(
        'Clients',
        'dateFormat',
        {
          type: Sequelize.ENUM(...Object.values(dateFormats)),
          allowNull: false,
          defaultValue: dateFormats['YYYY-MM-DD'],
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'Clients',
        'decimalPoint',
        {
          type: Sequelize.ENUM(...Object.values(decimalPoints)),
          allowNull: false,
          defaultValue: decimalPoints.SPACE,
        },
        { transaction },
      );
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // Remove the columns from Clients table
      await queryInterface.removeColumn('Clients', 'dateFormat', { transaction });
      await queryInterface.removeColumn('Clients', 'decimalPoint', { transaction });

      // Drop the enum types for Clients
      await queryInterface.sequelize.query('DROP TYPE "enum_Clients_dateFormat";', { transaction });
      await queryInterface.sequelize.query('DROP TYPE "enum_Clients_decimalPoint";', { transaction });

      // Note: PostgreSQL does not support removing enum values directly.
      // The 'space' value will remain in the Users enum even after rollback.
    });
  },
};
