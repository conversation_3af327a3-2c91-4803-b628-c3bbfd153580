const _ = require('lodash');
const { Op } = require('sequelize');

const { featureNames, rolesEnum, reportEnums, Client } = require('../enums');
const { sequelize } = require('../models');
const {
  featureRepository,
  guaranteeRepository,
  paymentRepository,
  userRepository,
  clientRepository,
} = require('../repositories');
const { getPayments } = require('../services');
const azureService = require('../services/azureService');
const redisService = require('../services/redis');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const {
  NotFoundError,
  ForbiddenError,
  InternalServerError,
  FinalizeUneditableError,
  MarkAsDraftUneditableError,
  BadRequestError,
} = require('../utils/ErrorHandler');
const { checkIsUsingRegionalProviderData } = require('../utils/featureUtils');
const guaranteeUtils = require('../utils/guaranteeUtils');
const {
  checkIsImported,
  getCalculationLogRedisKey,
  runReportUpdateGuards,
  checkCurrencyProviderDataAvailability,
  issueDateUpdateCheck,
} = require('../utils/reportUtils');
const { calculateGuaranteeDataAlgorithm } = require('./algorithms/reportAlgorithms');
const {
  prepareGuaranteeForCreation,
  mutateGuaranteeObjectWithAlgorithmData,
} = require('../utils/guaranteeUtils/prepareGuaranteeForCreation');

const { checkClientFeatureFlags } = require('../utils/clientUtils');

const orderByCreationDate = [['createdAt', 'DESC']];
const orderByDeleteDate = [['deletedAt', 'DESC']];

async function getGuarantees(req, res) {
  const { limit, isPortfolio } = req.query;
  const guarantees = await guaranteeRepository.getGuarantees({
    where: {
      clientId: req.user.clientId,
      isPortfolio: isPortfolio === 'true',
    },
    order: orderByCreationDate,
    limit: isNaN(limit) ? null : parseInt(limit),
  });
  res.json(guarantees);
}

async function getDeletedGuarantees(req, res) {
  const guarantees = await guaranteeRepository.getGuarantees({
    where: {
      clientId: req.user.clientId,
      deletedAt: { [Op.ne]: null },
    },
    order: orderByDeleteDate,
    paranoid: false,
  });
  res.json(guarantees);
}

async function getGuarantee(req, res) {
  const guarantee = await guaranteeRepository.getGuarantee(req.params.id, req.user.clientId);

  if (!guarantee) {
    throw new NotFoundError('Guarantee');
  }

  res.json(guarantee);
}

async function postGuarantee(req, res) {
  const guarantee = req.body;
  const { clientId } = req.user;

  await prepareGuaranteeForCreation(guarantee, clientId);

  res.json(await guaranteeRepository.createGuarantee(guarantee, req.user));
}

async function postGuarantees(req, res) {
  const guarantees = req.body;
  const { clientId } = req.user;

  await sequelize.transaction(async () => {
    await Promise.all(guarantees.map((guarantee) => prepareGuaranteeForCreation(guarantee, clientId)));

    await Promise.all(guarantees.map((guarantee) => guaranteeRepository.createGuarantee(guarantee, req.user)));

    res.status(204).send();
  });
}

async function runAlgorithm(req, res) {
  const guarantee = req.body;
  const { clientId } = req.user;
  const { id } = req.params;

  const isUsingRegionalProviderData = await checkIsUsingRegionalProviderData(clientId);

  const { report, calculationLog, pricingApproach } = await calculateGuaranteeDataAlgorithm({
    ...guarantee,
    isUsingRegionalProviderData,
  });

  const oneDaySeconds = 86400;

  const result = await redisService.setex(
    getCalculationLogRedisKey(reportEnums.REPORT_TYPES.GUARANTEE, id),
    oneDaySeconds,
    JSON.stringify(calculationLog),
  );

  if (result !== 'OK') {
    throw new InternalServerError('Saving to redis failed');
  }

  res.json({ report, pricingApproach });
}

async function postImportedGuarantee(req, res) {
  const guarantee = req.body;

  const [guarantorCreditRating, principalCreditRating] = guaranteeUtils.overrideCreditRatings(
    guarantee?.guarantor?.creditRating,
    guarantee?.principal?.creditRating,
  );
  guarantee.guarantor.creditRating = guarantorCreditRating;
  guarantee.principal.creditRating = principalCreditRating;

  res.json(await guaranteeRepository.createGuarantee(guarantee, req.user));
}

async function postImportedGuarantees(req, res) {
  const guarantees = req.body;

  await sequelize.transaction(async () => {
    const createGuaranteePromises = guarantees.map((guarantee) => {
      const [guarantorCreditRating, principalCreditRating] = guaranteeUtils.overrideCreditRatings(
        guarantee.guarantor.creditRating,
        guarantee.principal.creditRating,
      );
      guarantee.guarantor.creditRating = guarantorCreditRating;
      guarantee.principal.creditRating = principalCreditRating;
      return guaranteeRepository.createGuarantee(guarantee, req.user);
    });

    await Promise.all(createGuaranteePromises);
    res.status(204).send();
  });
}

async function putGuarantee(req, res) {
  const id = req.params.id;
  const guarantee = req.body;
  const { clientId } = req.user;

  const [oldGuarantee, clientFeature] = await Promise.all([
    guaranteeRepository.getGuarantee(id, clientId, null),
    featureRepository.getClientFeatureByName({ clientId, featureName: 'guaranteeNumber' }),
  ]);

  checkCurrencyProviderDataAvailability(guarantee);
  runReportUpdateGuards(oldGuarantee, 'guarantee');
  issueDateUpdateCheck(oldGuarantee, guarantee, clientFeature);

  const isUsingRegionalProviderData = await checkIsUsingRegionalProviderData(clientId);

  const algorithmData = await calculateGuaranteeDataAlgorithm({ ...guarantee, isUsingRegionalProviderData });

  mutateGuaranteeObjectWithAlgorithmData(guarantee, algorithmData);

  res.json(await guaranteeRepository.updateGuarantee(id, guarantee, req.user));
}

async function putGuaranteeSaveUpdated(req, res) {
  const { clientId } = req.user;
  const { id } = req.params;
  const guarantee = req.body;

  const oldGuarantee = await guaranteeRepository.getGuarantee(id, clientId, null);

  runReportUpdateGuards(oldGuarantee, 'guarantee');

  const calculationLog = await redisService.get(getCalculationLogRedisKey(reportEnums.REPORT_TYPES.GUARANTEE, id));

  if (!calculationLog) {
    throw new NotFoundError('Calculation log');
  }

  const paymentsGuaranteeObj = { ...oldGuarantee.dataValues, ...guarantee };
  const [totalInterest, guaranteePayments] = await getPayments({
    report: paymentsGuaranteeObj,
    finalInterestRate: guarantee.report.finalInterestRate,
    reportIdKey: 'guaranteeId',
  });
  guarantee.totalInterest = totalInterest;

  await sequelize.transaction(async () => {
    await paymentRepository.createPayments(guaranteePayments, 'Bullet');

    const guaranteeWithCalculationLog = { ...guarantee, isPortfolio: true, calculationLog: JSON.parse(calculationLog) };

    return res.json(await guaranteeRepository.updateGuarantee(id, guaranteeWithCalculationLog, req.user));
  });
}

async function putImportedGuarantee(req, res) {
  const id = req.params.id;
  const guarantee = req.body;

  const oldGuarantee = await guaranteeRepository.getGuarantee(id, req.user.clientId, null);

  runReportUpdateGuards(oldGuarantee, 'guarantee');

  const [guarantorCreditRating, principalCreditRating] = guaranteeUtils.overrideCreditRatings(
    guarantee?.guarantor?.creditRating,
    guarantee?.principal?.creditRating,
  );
  guarantee.guarantor.creditRating = guarantorCreditRating;
  guarantee.principal.creditRating = principalCreditRating;
  res.json(await guaranteeRepository.updateGuarantee(id, guarantee, req.user));
}

async function putGuaranteeRates(req, res) {
  const { id } = req.params;
  const { body } = req;

  const guarantee = (await guaranteeRepository.getGuarantee(id, req.user.clientId, null))?.dataValues;
  if (!guarantee) {
    throw new NotFoundError('Guarantee');
  }
  if (guarantee.isPortfolio) {
    throw new BadRequestError('Guarantee must be in analyses for rate changing');
  }

  guarantee.report = { ...guarantee.report, ...body };

  res.json(await guaranteeRepository.updateGuarantee(id, guarantee, req.user));
}

async function putGuaranteeStatus(req, res) {
  const id = req.params.id;
  const { status } = req.body;

  const guarantee = (await guaranteeRepository.getGuarantee(id, req.user.clientId, null))?.dataValues;

  if (!guarantee) {
    throw new NotFoundError('Guarantee');
  }

  if (!guarantee.editable) {
    if (status === 'Draft') {
      throw new MarkAsDraftUneditableError('guarantee');
    }
    throw new FinalizeUneditableError('guarantee');
  }

  guarantee.status = status;
  if (status === 'Final') {
    guarantee.finalizedBy = req.user?.username;
  } else {
    guarantee.finalizedBy = null;
  }

  res.json(await guaranteeRepository.updateGuarantee(id, guarantee, req.user));
}

async function putGuaranteeNote(req, res) {
  const id = req.params.id;
  const { note } = req.body;

  const guarantee = (await guaranteeRepository.getGuarantee(id, req.user.clientId, null))?.dataValues;

  if (guarantee) {
    if (guarantee.status === 'Final') {
      throw new BadRequestError('Cannot update note of finalized guarantee.');
    } else {
      guarantee.note = note;
      res.json(await guaranteeRepository.updateGuarantee(id, guarantee, req.user));
    }
  } else {
    throw new NotFoundError('Guarantee');
  }
}

async function generateAgreement(req, res) {
  const { clientId } = req.user;
  const guaranteeId = req.params.id;
  const guarantee = (await guaranteeRepository.getGuarantee(guaranteeId, clientId, null))?.dataValues;
  const client = await clientRepository.getClient(clientId);

  if (!guarantee) {
    throw new NotFoundError('Guarantee');
  }

  if (checkIsImported(guarantee.report)) {
    throw new BadRequestError("Can't generate agreement for imported guarantee");
  }

  if (guarantee.files.find(({ dataValues }) => dataValues.label === 'Agreement' && dataValues.isGenerated)) {
    throw new BadRequestError('Agreement already exists');
  }

  const isUsingRegionalProviderData = await checkIsUsingRegionalProviderData(clientId);

  const templateData = await guaranteeUtils.getTemplateData(guarantee, clientId, isUsingRegionalProviderData, client);

  await sequelize.transaction(async () => {
    const [agreementFile, agreementMetadata] = await guaranteeUtils.generateAgreement(
      guarantee,
      clientId,
      templateData,
    );
    guarantee.files = [agreementMetadata];
    const { dataValues } = await guaranteeRepository.updateGuaranteeAndCreateFiles(guaranteeId, guarantee, req.user);

    const fileId = dataValues.id;
    await azureService.uploadFile(`guarantee/${fileId}`, agreementFile, Buffer.byteLength(agreementFile));
    res.json(dataValues);
  });
}

async function generateTpReport(req, res) {
  const { clientId } = req.user;
  const { id } = req.params;
  const guarantee = (await guaranteeRepository.getGuarantee(id, clientId, null))?.dataValues;
  const client = await clientRepository.getClient(clientId);

  if (!guarantee) {
    throw new NotFoundError('Guarantee not found');
  }

  if (checkIsImported(guarantee.report)) {
    throw new BadRequestError("Can't generate TP Report for imported guarantee");
  }

  if (guarantee.files.find(({ dataValues }) => dataValues.label === 'TP Report' && dataValues.isGenerated)) {
    throw new BadRequestError('TP Report already exists');
  }

  const isUsingRegionalProviderData = await checkIsUsingRegionalProviderData(clientId);

  const templateData = await guaranteeUtils.getTemplateData(guarantee, clientId, isUsingRegionalProviderData, client);

  await sequelize.transaction(async () => {
    const [tpReportFile, tpReportMetadata] = await guaranteeUtils.generateTpReport(guarantee, clientId, templateData);
    guarantee.files = [tpReportMetadata];
    const { dataValues } = await guaranteeRepository.updateGuaranteeAndCreateFiles(id, guarantee, req.user);

    const fileId = dataValues.id;
    await azureService.uploadFile(`guarantee/${fileId}`, tpReportFile, Buffer.byteLength(tpReportFile));
    res.json(dataValues);
  });
}

async function putGuaranteeIsPortfolio(req, res) {
  const id = req.params.id;
  const { isPortfolio } = req.body;

  const guarantee = (await guaranteeRepository.getGuarantee(id, req.user.clientId, null))?.dataValues;
  const finalInterestRate = guarantee.report.finalInterestRate;

  runReportUpdateGuards(guarantee, 'guarantee');

  if (guarantee.report?.upperBound < 0 || guarantee.report?.lowerBound > guarantee.report?.upperBound) {
    throw new BadRequestError('Cannot move guarantee that would not be agreed at arms length to portfolio.');
  }

  await sequelize.transaction(async () => {
    if (!isPortfolio) {
      guarantee.movedToAnalysesDate = new Date();
      guarantee.totalInterest = null;
      await paymentRepository.deletePayments({ guaranteeId: id });
    }
    if (isPortfolio) {
      const [totalInterest, payments] = await getPayments({
        report: guarantee,
        finalInterestRate,
        reportIdKey: 'guaranteeId',
      });
      guarantee.totalInterest = totalInterest;
      await paymentRepository.createPayments(payments, 'Bullet');
    }

    guarantee.isPortfolio = isPortfolio;

    await guaranteeRepository.updateGuarantee(id, guarantee, req.user, false);
    return res.status(204).send();
  });
}

async function deleteGuarantee(req, res) {
  const { role, clientId } = req.user;
  const force = req.query.force === 'true';
  const guaranteeId = req.params.id;
  const isUser = role === rolesEnum.USER;

  const guarantee = await guaranteeRepository.getGuarantee(guaranteeId, clientId);

  if (!guarantee) throw new NotFoundError('Guarantee');
  const feature = await featureRepository.getClientFeatureByName({
    clientId,
    featureName: featureNames.GUARANTEE_NUMBER,
  });
  if (guarantee.isPortfolio && isUser) throw new ForbiddenError('Only admins can delete a guarantee in portfolio');
  if (force && isUser) throw new ForbiddenError('Only admins can permanently delete guarantees');
  if (feature.isEnabled && force) throw new ForbiddenError('Cannot permanently delete guarantee.');

  await sequelize.transaction(async () => {
    if (force) await Promise.all(guarantee.files.map((file) => azureService.deleteFile(`guarantee/${file.id}`)));

    await guaranteeRepository.deleteGuarantee(guaranteeId, clientId, force);
  });

  res.status(204).send();
}

async function restoreGuarantee(req, res) {
  const { role, clientId } = req.user;
  const guaranteeId = req.params.id;
  const isUser = role === rolesEnum.USER;

  const guarantee = await guaranteeRepository.getGuarantee(guaranteeId, clientId);

  if (!guarantee) throw new NotFoundError('Guarantee');
  if (isUser) throw new ForbiddenError('Only admins can restore a guarantee');

  await guaranteeRepository.restoreGuarantee(guaranteeId, clientId);

  res.status(204).send();
}

function getImportTemplate(req, res) {
  res.set('Content-Type', 'application/vnd.ms-excel');
  res.download('./static/guarantee_import_template.xlsx');
}

function getUploadTemplate(req, res) {
  res.set('Content-Type', 'application/vnd.ms-excel');
  res.download('./static/guarantee_upload_template.xlsx');
}

module.exports = {
  getGuarantees: asyncControllerWrapper(getGuarantees),
  getDeletedGuarantees: asyncControllerWrapper(getDeletedGuarantees),
  getGuarantee: asyncControllerWrapper(getGuarantee),
  getImportTemplate: asyncControllerWrapper(getImportTemplate),
  getUploadTemplate: asyncControllerWrapper(getUploadTemplate),
  postGuarantee: asyncControllerWrapper(postGuarantee),
  postGuarantees: asyncControllerWrapper(postGuarantees),
  runAlgorithm: asyncControllerWrapper(runAlgorithm),
  postImportedGuarantee: asyncControllerWrapper(postImportedGuarantee),
  postImportedGuarantees: asyncControllerWrapper(postImportedGuarantees),
  putGuarantee: asyncControllerWrapper(putGuarantee),
  putGuaranteeSaveUpdated: asyncControllerWrapper(putGuaranteeSaveUpdated),
  putImportedGuarantee: asyncControllerWrapper(putImportedGuarantee),
  putGuaranteeRates: asyncControllerWrapper(putGuaranteeRates),
  putGuaranteeStatus: asyncControllerWrapper(putGuaranteeStatus),
  putGuaranteeNote: asyncControllerWrapper(putGuaranteeNote),
  putGuaranteeIsPortfolio: asyncControllerWrapper(putGuaranteeIsPortfolio),
  generateTpReport: asyncControllerWrapper(generateTpReport),
  deleteGuarantee: asyncControllerWrapper(deleteGuarantee),
  restoreGuarantee: asyncControllerWrapper(restoreGuarantee),
  generateAgreement: asyncControllerWrapper(generateAgreement),
};
