'use strict';
import { dateFormats, decimalPoints } from '../enums';

const Client = (sequelize: any, DataTypes: any) => {
  const Client = sequelize.define(
    'Client',
    {
      name: {
        type: DataTypes.STRING,
        unique: true,
      },
      industry: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'Unknown',
      },
      emailDomains: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
      },
      isLoanApproachCalculated: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      isCreditRatingAnonymized: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      dateFormat: {
        type: DataTypes.ENUM(...Object.values(dateFormats)),
        allowNull: false,
        defaultValue: dateFormats['YYYY-MM-DD'],
      },
      decimalPoint: {
        type: DataTypes.ENUM(...Object.values(decimalPoints)),
        allowNull: false,
        defaultValue: decimalPoints.SPACE,
      },
    },
    {},
  );

  Client.associate = function (models: any) {
    Client.hasMany(models.User, {
      foreignKey: 'clientId',
      sourceKey: 'id',
      as: 'users',
    });

    Client.hasMany(models.Company, {
      foreignKey: 'clientId',
      sourceKey: 'id',
    });

    Client.hasMany(models.CreditRating, {
      foreignKey: 'clientId',
    });

    Client.hasMany(models.Loan, {
      foreignKey: 'clientId',
    });

    Client.hasMany(models.BackToBackLoan, {
      foreignKey: 'clientId',
    });

    Client.hasMany(models.Guarantee, {
      foreignKey: 'clientId',
    });

    Client.hasMany(models.Client_Feature, {
      foreignKey: 'clientId',
      as: 'clientFeatures',
    });

    Client.hasMany(models.CashPool, {
      foreignKey: 'clientId',
    });

    Client.hasMany(models.TemplateFile, {
      foreignKey: 'clientId',
    });
  };

  return Client;
};

export = Client;
