import type { DateFormatType, SingleSelectWidthType } from 'types';
import { SingleSelect } from 'ui';
import { getOptionsFromArray } from 'utils/arrays';

const dateFormats = ['YYYY-MM-DD', 'DD-MM-YYYY', 'MM-DD-YYYY', 'DD MONTH YYYY'];
const options = getOptionsFromArray(dateFormats);

type DateFormatSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  value: DateFormatType;
  onChange: Function;
  tooltip?: string;
};

const DateFormatSingleSelect = ({
  label = 'Date format',
  value,
  width = 'm',
  onChange,
  tooltip,
}: DateFormatSingleSelectProps) => (
  <SingleSelect label={label} options={options} tooltip={tooltip} value={value} width={width} onChange={onChange} />
);

export default DateFormatSingleSelect;
