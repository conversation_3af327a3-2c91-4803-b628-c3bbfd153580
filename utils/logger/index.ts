/* eslint-disable no-console */
import { consoleLogger, fileLogger } from './pino';
import sentry from './sentry';

const getErrorMessage = (message: string, isError?: boolean) => `${isError ? 'ERR_MSG' : 'WRN_MSG'} : ${message || ''}`;

const isEnabled = process.env.SENTRY_ENABLED === '1';

interface LoggerArgs {
  error: Error;
  message: string;
  payload?: any;
}

export = {
  info: consoleLogger.info.bind(consoleLogger),
  toFile: (message: string, options?: Partial<{ dest: string; type: 'info' | 'error' }>) => {
    const dest = options?.dest || 'output.log';
    const type = options?.type || 'info';

    const logger = fileLogger(String(dest));

    if (type === 'error') logger.error(message);
    if (type === 'info') logger.info(message);
  },
  warning: ({ message: originalMessage, error, payload }: LoggerArgs) => {
    const message = getErrorMessage(originalMessage);
    if (isEnabled) {
      sentry.warning({
        message,
        error,
        payload,
        errorStack: error ? error.stack : '',
      });
    } else {
      console.warn(message, { message, error, payload });
    }
  },
  error: ({ message: originalMessage, error, payload }: LoggerArgs) => {
    const message = getErrorMessage(originalMessage, true);
    if (isEnabled) {
      sentry.error({
        message,
        error,
        payload,
        errorStack: error ? error.stack : '',
      });
    } else {
      // console.error(message, { message, error, payload });
    }
  },
};
