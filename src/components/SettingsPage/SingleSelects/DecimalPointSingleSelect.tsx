import { COMMA, DOT, SPACE } from 'enums';
import type { DecimalPointType, SingleSelectWidthType } from 'types';
import { SingleSelect } from 'ui';

const options = [
  { label: 'US/UK (1,000.00)', value: DOT },
  { label: 'European (1.000,00)', value: COMMA },
  { label: 'Space (1 000,00)', value: SPACE },
];

type DecimalPointSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  value: DecimalPointType;
  onChange: Function;
  tooltip?: string;
};

const DecimalPointSingleSelect = ({
  label = 'Decimal & Thousand Separators',
  value,
  width = 'm',
  onChange,
  tooltip,
}: DecimalPointSingleSelectProps) => (
  <SingleSelect label={label} tooltip={tooltip} options={options} value={value} width={width} onChange={onChange} />
);

export default DecimalPointSingleSelect;
