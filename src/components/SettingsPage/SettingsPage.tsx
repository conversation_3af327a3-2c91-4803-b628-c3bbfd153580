import { useContext, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { loginAsAnyone, updateSettings } from 'api';
import { setAccessToken, setAuthMethod } from 'auth';
import { UserInfoContext } from 'context/user';
import { rolesEnum } from 'enums';
import { clearErrors, setFieldError, setSettings, settingsSelector, updateField } from 'reducers/settings.slice';
import { routesEnum } from 'routes';
import type { UpdateUserSettingsDataType, UserType } from 'types';
import { Box, Button, Card, FlexLayout, Text, TextInput } from 'ui';
import { showErrorToast, showToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';

import { DateFormatSingleSelect, DecimalPointSingleSelect /* , TimeZoneSingleSelect */ } from './SingleSelects';

const SettingsPage = () => {
  const { userInfo, setUserInfo } = useContext(UserInfoContext);
  const {
    email,
    username,
    fullName,
    dateFormat,
    decimalPoint,
    timezone,
    oldPassword,
    newPassword,
    confirmPassword,
    errors,
    loginAsAnyoneEmail,
    loginAsAnyonePassword,
  } = useSelector(settingsSelector);
  const dispatch = useDispatch();
  const history = useHistory();

  const onSave = () => {
    dispatch(clearErrors());
    if (newPassword && oldPassword && confirmPassword) {
      if (newPassword.length < 12) {
        return dispatch(
          setFieldError({ field: 'newPassword', message: 'Password has to be 12 characters or longer.' })
        );
      }
      if (!newPassword.match(/[0-9]/)) {
        return dispatch(
          setFieldError({ field: 'newPassword', message: 'Password has to contain at least one number.' })
        );
      }
      if (!newPassword.match(/[a-z]/)) {
        return dispatch(
          setFieldError({ field: 'newPassword', message: 'Password has to contain at least one lowercase letter.' })
        );
      }
      if (!newPassword.match(/[A-Z]/)) {
        return dispatch(
          setFieldError({ field: 'newPassword', message: 'Password has to contain at least one uppercase letter.' })
        );
      }
      if (!newPassword.match(/[!@#$%^&*]/)) {
        return dispatch(
          setFieldError({ field: 'newPassword', message: 'Password has to contain at least one special character.' })
        );
      }

      if (newPassword !== confirmPassword) {
        return dispatch(
          setFieldError({ field: 'confirmPassword', message: 'Password and confirm password do not match' })
        );
      }
    }
    const data: UpdateUserSettingsDataType = { fullName, dateFormat, decimalPoint, timezone };
    if (oldPassword && newPassword) {
      data.oldPassword = oldPassword;
      data.newPassword = newPassword;
    }

    updateSettings(data)
      .then((user: UserType) => {
        setUserInfo({ ...userInfo, ...user });
        showToast('Updated successfully');
      })
      .catch((err: any) => {
        if (err.response.data.statusCode === 400) {
          return dispatch(setFieldError({ field: 'oldPassword', message: err.response.data.message }));
        }
        showErrorToast();
      });
  };

  const onLoginAsAnyone = async () => {
    try {
      const data = { email: loginAsAnyoneEmail, password: loginAsAnyonePassword };
      const { accessToken } = await loginAsAnyone(data);
      setAccessToken(accessToken);
      setAuthMethod('default');
      window.location.href = routesEnum.DASHBOARD;
    } catch (err) {
      errorHandler(err);
    }
  };

  useEffect(() => {
    dispatch(setSettings(userInfo));
  }, [userInfo, dispatch]);

  return (
    <>
      <Card p={6} space={4}>
        <Text variant="2l-spaced" color="deep-sapphire">
          Profile Info
        </Text>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <FlexLayout space={8}>
            <TextInput label="Email" width="l" disabled withLock isUnlockable={false} value={email} />
            {username && <TextInput label="Username" disabled withLock isUnlockable={false} value={username} />}
            <TextInput
              label="Full Name"
              value={fullName}
              onChange={(fullName: string) => dispatch(updateField({ fullName }))}
            />
          </FlexLayout>
        </FlexLayout>
      </Card>

      <Card p={6} space={4}>
        <Text variant="2l-spaced" color="deep-sapphire">
          Date &amp; Number Formats {'(display)'}
        </Text>
        <FlexLayout space={8}>
          <DateFormatSingleSelect
            value={dateFormat}
            onChange={(dateFormat: Date) => dispatch(updateField({ dateFormat }))}
            tooltip="Sets how dates appear in your user interface only. <br /> This setting won't affect dates in exported reports or documents."
          />
          <DecimalPointSingleSelect
            value={decimalPoint}
            onChange={(decimalPoint: string) => dispatch(updateField({ decimalPoint }))}
            tooltip="Sets how numbers display in your interface only (e.g., 1,000.00 vs 1.000,00). <br /> This setting won't affect exported reports or documents."
          />
          {/* <TimeZoneSingleSelect
            value={timezone}
            onChange={(timezone: string) => dispatch(updateField({ timezone }))}
            width="l"
          /> */}
        </FlexLayout>
      </Card>

      {username && (
        <Card p={6} space={4}>
          <Text variant="2l-spaced" color="deep-sapphire">
            Change password
          </Text>
          <FlexLayout alignItems="center" justifyContent="space-between">
            <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(3, 1fr)' }}>
              <TextInput
                label="Current Password"
                type="password"
                variant="password"
                value={oldPassword}
                onChange={(oldPassword: string) => dispatch(updateField({ oldPassword }))}
                error={errors?.oldPassword}
              />
              <TextInput
                label="New Password"
                type="password"
                variant="password"
                value={newPassword}
                onChange={(newPassword: string) => dispatch(updateField({ newPassword }))}
                error={errors?.newPassword}
              />
              <TextInput
                label="Confirm Password"
                type="password"
                variant="password"
                value={confirmPassword}
                onChange={(confirmPassword: string) => dispatch(updateField({ confirmPassword }))}
                error={errors?.confirmPassword}
              />
            </Box>
          </FlexLayout>
        </Card>
      )}

      {userInfo.role === rolesEnum.SUPERADMIN && (
        <Card p={6} space={4}>
          <Text variant="2l-spaced" color="deep-sapphire">
            Master
          </Text>
          <FlexLayout alignItems="flex-end" space={8}>
            <TextInput
              label="Email"
              type="email"
              value={loginAsAnyoneEmail}
              onChange={(loginAsAnyoneEmail: string) => dispatch(updateField({ loginAsAnyoneEmail }))}
            />
            <TextInput
              label="Password"
              type="password"
              variant="password"
              value={loginAsAnyonePassword}
              onChange={(loginAsAnyonePassword: string) => dispatch(updateField({ loginAsAnyonePassword }))}
            />
            <Button text="Login" onClick={onLoginAsAnyone} disabled={!loginAsAnyoneEmail || !loginAsAnyonePassword} />
          </FlexLayout>
        </Card>
      )}

      <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
        <Button text="Back" variant="gray" onClick={history.goBack} />
        <Button text="Save" onClick={onSave} />
      </FlexLayout>
    </>
  );
};

export default SettingsPage;
